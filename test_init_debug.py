#!/usr/bin/env python3
"""
测试初始化流程的调试脚本
用于验证系统初始化是否会卡住
"""

import asyncio
import time
import sys
import os

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'main/xiaozhi-server'))

async def test_mcp_init():
    """测试MCP初始化流程"""
    print("🔧 开始测试MCP初始化流程...")
    
    try:
        # 模拟创建一个简单的连接对象
        class MockConn:
            def __init__(self):
                self.logger = self
                self.func_handler = None
                
            def bind(self, tag=None):
                return self
                
            def info(self, msg):
                print(f"[INFO] {msg}")
                
            def warning(self, msg):
                print(f"[WARNING] {msg}")
                
            def error(self, msg):
                print(f"[ERROR] {msg}")
                
            def debug(self, msg):
                print(f"[DEBUG] {msg}")
        
        # 创建模拟连接
        conn = MockConn()
        
        # 测试MCPManager初始化
        print("🔧 测试MCPManager创建...")
        from core.mcp.manager import MCPManager
        
        start_time = time.time()
        mcp_manager = MCPManager(conn)
        create_time = time.time() - start_time
        print(f"✅ MCPManager创建完成，耗时: {create_time:.2f}秒")
        
        # 测试MCP服务器初始化
        print("🔧 测试MCP服务器初始化...")
        start_time = time.time()
        
        # 设置超时
        try:
            await asyncio.wait_for(mcp_manager.initialize_servers(), timeout=10)
            init_time = time.time() - start_time
            print(f"✅ MCP服务器初始化完成，耗时: {init_time:.2f}秒")
        except asyncio.TimeoutError:
            print("❌ MCP服务器初始化超时（10秒）")
        except Exception as e:
            init_time = time.time() - start_time
            print(f"⚠️ MCP服务器初始化异常（耗时: {init_time:.2f}秒）: {e}")
            
    except Exception as e:
        print(f"❌ 测试过程中发生异常: {e}")
        import traceback
        traceback.print_exc()

async def main():
    """主函数"""
    print("🚀 开始初始化流程调试测试")
    print("=" * 50)
    
    await test_mcp_init()
    
    print("=" * 50)
    print("🎉 测试完成")

if __name__ == "__main__":
    asyncio.run(main())
