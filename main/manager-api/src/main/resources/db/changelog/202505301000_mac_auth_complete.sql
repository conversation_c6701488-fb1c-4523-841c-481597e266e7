-- MAC地址认证系统完整数据库变更
-- changeSet ID: 202505301000_mac_auth_complete
-- 作者: simonchen
-- 目的: MAC地址认证系统完整实现（合并优化版本）
-- 合并原变更集: 202505301000, 202506151000, 202506151100, 202505231110

-- ==================== 表结构检查和创建 ====================
-- 注意：所有主要表已存在，这里只确保必要的表存在

-- 确保配额设置表存在（已存在，跳过）
-- 确保配额使用记录表存在（已存在，跳过）
-- 确保MAC地址黑名单表存在（已存在，跳过）
-- 确保MAC地址白名单表存在（已存在，跳过）
-- 确保设备指纹表存在（已存在，跳过）

SELECT 'MAC认证相关表已存在，跳过表创建' AS message;

-- ==================== 索引优化 ====================

-- 添加设备表MAC地址索引（如果不存在）
SET @indexExists = (
    SELECT COUNT(1)
    FROM information_schema.statistics
    WHERE table_schema = DATABASE()
    AND table_name = 'ai_device'
    AND index_name = 'idx_ai_device_mac_address'
);

SET @sqlStatement = IF(@indexExists = 0,
    'ALTER TABLE `ai_device` ADD INDEX `idx_ai_device_mac_address` (`mac_address`) COMMENT "MAC地址索引"',
    'SELECT "索引 idx_ai_device_mac_address 已存在，跳过创建" AS message'
);

PREPARE stmt FROM @sqlStatement;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- ==================== 初始化数据 ====================

-- 清理并重建配额设置
DELETE FROM `ai_quota_settings` WHERE `quota_type` IN ('device', 'account');

-- 初始化配额设置
INSERT INTO `ai_quota_settings` (`quota_type`, `quota_value`, `reset_type`, `description`, `create_date`)
VALUES
('device', 10000, 'daily', '设备每日配额', NOW()),
('account', 100000, 'daily', '账号每日配额', NOW());

-- ==================== 系统参数配置 ====================

-- 彻底清理所有可能存在冲突的参数
DELETE FROM `sys_params` WHERE `param_code` IN (
    -- 旧的MAC认证参数
    'mac_access_limit', 'enable_mac_auth', 'enable_quota_limit',
    'mac_auth_enabled', 'mac_auto_register', 'device_fingerprint_enabled',
    'mac_auto_register_enabled',
    -- 新的MAC认证参数
    'mac_auth.enabled', 'mac_auth.access_limit', 'mac_auth.cache_ttl',
    'mac_auth.auto_register', 'mac_auth.enable_device_fingerprint',
    -- 配额相关参数
    'quota.enabled', 'quota.device_default_quota', 'quota.account_default_quota'
);

-- 清理可能存在冲突的ID范围
DELETE FROM `sys_params` WHERE `id` BETWEEN 1001 AND 1008;

-- MAC认证核心参数（统一使用mac_auth.*命名规范）
INSERT INTO `sys_params` (`id`, `param_code`, `param_value`, `value_type`, `param_type`, `remark`, `create_date`) VALUES
(1001, 'mac_auth.enabled', 'true', 'boolean', 1, 'MAC地址认证启用状态', NOW()),
(1002, 'mac_auth.access_limit', '10', 'number', 1, 'MAC地址访问频率限制（次/分钟）', NOW()),
(1003, 'mac_auth.cache_ttl', '300', 'number', 1, 'MAC地址缓存有效期（秒）', NOW()),
(1004, 'mac_auth.auto_register', 'true', 'boolean', 1, '是否允许MAC地址自动注册', NOW()),
(1005, 'mac_auth.enable_device_fingerprint', 'true', 'boolean', 1, 'MAC地址设备指纹验证启用状态', NOW());

-- 配额管理参数
INSERT INTO `sys_params` (`id`, `param_code`, `param_value`, `value_type`, `param_type`, `remark`, `create_date`) VALUES
(1006, 'quota.enabled', 'true', 'boolean', 1, '是否启用配额限制', NOW()),
(1007, 'quota.device_default_quota', '10000', 'number', 1, '设备默认每日配额', NOW()),
(1008, 'quota.account_default_quota', '100000', 'number', 1, '账号默认每日配额', NOW());

-- ==================== 验证结果 ====================

-- 验证表创建结果
SELECT
    TABLE_NAME as '表名',
    TABLE_COMMENT as '表注释',
    TABLE_ROWS as '行数'
FROM information_schema.TABLES
WHERE TABLE_SCHEMA = DATABASE()
AND TABLE_NAME IN (
    'ai_quota_settings', 'ai_quota_usage', 'ai_mac_blacklist',
    'ai_mac_whitelist', 'ai_device_fingerprint'
)
ORDER BY TABLE_NAME;

-- 验证系统参数配置
SELECT
    param_code as '参数代码',
    param_value as '参数值',
    remark as '说明'
FROM sys_params
WHERE param_code LIKE 'mac_auth%' OR param_code LIKE 'quota%'
ORDER BY param_code;