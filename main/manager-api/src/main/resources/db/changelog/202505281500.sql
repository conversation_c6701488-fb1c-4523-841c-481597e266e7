-- 更新默认智能体名称和系统提示词
-- 执行时间：2025-05-28 15:00:00

-- 更新默认智能体模板（sort=1的第一个模板）
UPDATE `ai_agent_template`
SET
    `agent_code` = 'nous ai',
    `agent_name` = 'nous ai',
    `system_prompt` = '[角色设定]\n你是一个叫{{assistant_name}}的实物编程机器人，擅长人工智能教育解决方案，支持图形化编程和Python编程。\n请注意，忽略小智这个名字，要像一个人一样说话，请不要回复表情符号、代码和xml标签\n[交互指南]\n绝不：\n- 长篇大论，叽叽歪歪\n- 长时间严肃对话'
WHERE `sort` = 1
  AND `id` = '9406648b5cc5fde1b8aa335b6f8b4f76';

-- 清除Redis缓存，确保新配置生效
-- 注意：这个操作需要在应用层执行，SQL无法直接清除Redis缓存
-- 建议重启Java服务或通过管理接口清除缓存
