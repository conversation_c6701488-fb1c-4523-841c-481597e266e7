-- Token限制架构修改 - 数据库迁移脚本
-- 版本: 202505281800
-- 描述: 从次数限制改为Token限制的完整数据库结构变更

-- =============================================================================
-- 步骤1: 添加新Token字段到ai_quota_usage表
-- =============================================================================

-- 检查并添加Token相关字段（避免重复添加）
SET @input_tokens_exists = (
    SELECT COUNT(1)
    FROM information_schema.COLUMNS
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'ai_quota_usage'
    AND COLUMN_NAME = 'input_tokens'
);

SET @output_tokens_exists = (
    SELECT COUNT(1)
    FROM information_schema.COLUMNS
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'ai_quota_usage'
    AND COLUMN_NAME = 'output_tokens'
);

SET @total_tokens_exists = (
    SELECT COUNT(1)
    FROM information_schema.COLUMNS
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'ai_quota_usage'
    AND COLUMN_NAME = 'total_tokens'
);

SET @model_name_exists = (
    SELECT COUNT(1)
    FROM information_schema.COLUMNS
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'ai_quota_usage'
    AND COLUMN_NAME = 'model_name'
);

-- 添加input_tokens字段
SET @sql = IF(@input_tokens_exists = 0,
    'ALTER TABLE ai_quota_usage ADD COLUMN input_tokens INT DEFAULT 0 COMMENT "输入Token数量"',
    'SELECT "字段input_tokens已存在，跳过添加" AS message'
);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加output_tokens字段
SET @sql = IF(@output_tokens_exists = 0,
    'ALTER TABLE ai_quota_usage ADD COLUMN output_tokens INT DEFAULT 0 COMMENT "输出Token数量"',
    'SELECT "字段output_tokens已存在，跳过添加" AS message'
);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加total_tokens字段
SET @sql = IF(@total_tokens_exists = 0,
    'ALTER TABLE ai_quota_usage ADD COLUMN total_tokens INT DEFAULT 0 COMMENT "Token总量"',
    'SELECT "字段total_tokens已存在，跳过添加" AS message'
);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 添加model_name字段
SET @sql = IF(@model_name_exists = 0,
    'ALTER TABLE ai_quota_usage ADD COLUMN model_name VARCHAR(100) COMMENT "模型名称"',
    'SELECT "字段model_name已存在，跳过添加" AS message'
);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- =============================================================================
-- 步骤2: 更新ai_agent表支持默认智能体
-- =============================================================================

-- 检查并添加默认智能体标识字段
SET @is_default_exists = (
    SELECT COUNT(1)
    FROM information_schema.COLUMNS
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'ai_agent'
    AND COLUMN_NAME = 'is_default'
);

SET @sql = IF(@is_default_exists = 0,
    'ALTER TABLE ai_agent ADD COLUMN is_default TINYINT(1) DEFAULT 0 COMMENT "是否为默认智能体(1-是, 0-否)"',
    'SELECT "字段is_default已存在，跳过添加" AS message'
);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- =============================================================================
-- 步骤3: 创建默认智能体配置表
-- =============================================================================

CREATE TABLE IF NOT EXISTS ai_default_agent (
    id VARCHAR(50) PRIMARY KEY COMMENT '默认智能体唯一标识',
    agent_code VARCHAR(50) NOT NULL COMMENT '智能体编码',
    name VARCHAR(100) NOT NULL COMMENT '智能体名称',
    description TEXT COMMENT '智能体描述',
    asr_model_id VARCHAR(50) COMMENT '语音识别模型标识',
    vad_model_id VARCHAR(50) COMMENT '语音活动检测标识',
    llm_model_id VARCHAR(50) COMMENT '大语言模型标识',
    tts_model_id VARCHAR(50) COMMENT '语音合成模型标识',
    tts_voice_id VARCHAR(50) COMMENT '音色标识',
    mem_model_id VARCHAR(50) COMMENT '记忆模型标识',
    intent_model_id VARCHAR(50) COMMENT '意图模型标识',
    system_prompt TEXT COMMENT '角色设定参数',
    lang_code VARCHAR(10) COMMENT '语言编码',
    language VARCHAR(50) COMMENT '交互语种',
    enabled TINYINT(1) DEFAULT 1 COMMENT '是否启用(1-启用, 0-禁用)',
    sort INT DEFAULT 0 COMMENT '排序',
    creator BIGINT COMMENT '创建者',
    create_date DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updater BIGINT COMMENT '更新者',
    update_date DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='默认智能体配置表';

-- =============================================================================
-- 步骤4: 创建索引优化查询性能
-- =============================================================================

-- 配额使用记录表索引（使用存储过程方式安全创建）
SET @sql = 'CREATE INDEX idx_quota_usage_agent_date ON ai_quota_usage(agent_id, usage_date)';
SET @index_exists = (
    SELECT COUNT(1)
    FROM information_schema.statistics
    WHERE table_schema = DATABASE()
    AND table_name = 'ai_quota_usage'
    AND index_name = 'idx_quota_usage_agent_date'
);
SET @sql = IF(@index_exists = 0, @sql, 'SELECT "索引idx_quota_usage_agent_date已存在" AS message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = 'CREATE INDEX idx_quota_usage_device_date ON ai_quota_usage(device_mac, usage_date)';
SET @index_exists = (
    SELECT COUNT(1)
    FROM information_schema.statistics
    WHERE table_schema = DATABASE()
    AND table_name = 'ai_quota_usage'
    AND index_name = 'idx_quota_usage_device_date'
);
SET @sql = IF(@index_exists = 0, @sql, 'SELECT "索引idx_quota_usage_device_date已存在" AS message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = 'CREATE INDEX idx_quota_usage_user_date ON ai_quota_usage(user_id, usage_date)';
SET @index_exists = (
    SELECT COUNT(1)
    FROM information_schema.statistics
    WHERE table_schema = DATABASE()
    AND table_name = 'ai_quota_usage'
    AND index_name = 'idx_quota_usage_user_date'
);
SET @sql = IF(@index_exists = 0, @sql, 'SELECT "索引idx_quota_usage_user_date已存在" AS message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = 'CREATE INDEX idx_quota_usage_model ON ai_quota_usage(model_name)';
SET @index_exists = (
    SELECT COUNT(1)
    FROM information_schema.statistics
    WHERE table_schema = DATABASE()
    AND table_name = 'ai_quota_usage'
    AND index_name = 'idx_quota_usage_model'
);
SET @sql = IF(@index_exists = 0, @sql, 'SELECT "索引idx_quota_usage_model已存在" AS message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 智能体表索引
SET @sql = 'CREATE INDEX idx_agent_is_default ON ai_agent(is_default)';
SET @index_exists = (
    SELECT COUNT(1)
    FROM information_schema.statistics
    WHERE table_schema = DATABASE()
    AND table_name = 'ai_agent'
    AND index_name = 'idx_agent_is_default'
);
SET @sql = IF(@index_exists = 0, @sql, 'SELECT "索引idx_agent_is_default已存在" AS message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = 'CREATE INDEX idx_agent_user_id ON ai_agent(user_id)';
SET @index_exists = (
    SELECT COUNT(1)
    FROM information_schema.statistics
    WHERE table_schema = DATABASE()
    AND table_name = 'ai_agent'
    AND index_name = 'idx_agent_user_id'
);
SET @sql = IF(@index_exists = 0, @sql, 'SELECT "索引idx_agent_user_id已存在" AS message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 设备表索引
SET @sql = 'CREATE INDEX idx_device_agent_id ON ai_device(agent_id)';
SET @index_exists = (
    SELECT COUNT(1)
    FROM information_schema.statistics
    WHERE table_schema = DATABASE()
    AND table_name = 'ai_device'
    AND index_name = 'idx_device_agent_id'
);
SET @sql = IF(@index_exists = 0, @sql, 'SELECT "索引idx_device_agent_id已存在" AS message');
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- =============================================================================
-- 步骤5: 插入官方默认智能体
-- =============================================================================

-- 插入官方默认智能体（如果不存在）
INSERT IGNORE INTO ai_agent (
    id,
    agent_code,
    agent_name,
    user_id,
    is_default,
    asr_model_id,
    vad_model_id,
    llm_model_id,
    tts_model_id,
    system_prompt,
    lang_code,
    language,
    sort,
    creator,
    created_at
) VALUES (
    'OFFICIAL_DEFAULT_AGENT',
    'OFFICIAL',
    '官方默认智能体',
    NULL,
    1,
    'sense_voice_small',
    'silero_vad',
    'deepseek_chat',
    'edge_tts',
    '你是小智，一个友好、智能的AI助手。请用简洁明了的方式回答用户的问题。',
    'zh',
    '中文',
    0,
    1,
    NOW()
);

-- 更新已存在的记录
UPDATE ai_agent
SET
    agent_name = '官方默认智能体',
    is_default = 1,
    update_date = NOW()
WHERE id = 'OFFICIAL_DEFAULT_AGENT';

-- =============================================================================
-- 步骤6: 更新设备默认绑定到官方智能体
-- =============================================================================

-- 将现有设备绑定到官方默认智能体
UPDATE ai_device
SET agent_id = 'OFFICIAL_DEFAULT_AGENT'
WHERE agent_id IS NULL OR agent_id = '';

-- =============================================================================
-- 步骤7: 数据迁移 - 将历史usageValue转换为Token字段
-- =============================================================================

-- 将历史usageValue转换为totalTokens
UPDATE ai_quota_usage
SET
    total_tokens = COALESCE(usage_value, 0),
    input_tokens = CASE
        WHEN usage_type = 'input' THEN COALESCE(usage_value, 0)
        ELSE 0
    END,
    output_tokens = CASE
        WHEN usage_type = 'output' THEN COALESCE(usage_value, 0)
        ELSE 0
    END
WHERE total_tokens IS NULL OR total_tokens = 0;

-- =============================================================================
-- 步骤8: 创建账号Token使用量累计视图
-- =============================================================================

CREATE OR REPLACE VIEW v_account_token_usage AS
SELECT
    a.user_id,
    qu.usage_date,
    SUM(qu.input_tokens) as total_input_tokens,
    SUM(qu.output_tokens) as total_output_tokens,
    SUM(qu.total_tokens) as total_tokens
FROM ai_quota_usage qu
INNER JOIN ai_agent a ON qu.agent_id = a.id
WHERE a.user_id IS NOT NULL  -- 排除默认智能体
GROUP BY a.user_id, qu.usage_date;

-- =============================================================================
-- 步骤9: 创建设备Token使用量视图
-- =============================================================================

CREATE OR REPLACE VIEW v_device_token_usage AS
SELECT
    qu.device_mac,
    qu.usage_date,
    SUM(qu.input_tokens) as total_input_tokens,
    SUM(qu.output_tokens) as total_output_tokens,
    SUM(qu.total_tokens) as total_tokens,
    qu.agent_id
FROM ai_quota_usage qu
INNER JOIN ai_agent a ON qu.agent_id = a.id
WHERE a.is_default = 1  -- 仅统计默认智能体的使用量
GROUP BY qu.device_mac, qu.usage_date, qu.agent_id;

-- =============================================================================
-- 完成标记
-- =============================================================================

-- 插入迁移完成标记（如果不存在）
INSERT IGNORE INTO sys_params (param_code, param_value, remark, creator, create_date)
VALUES ('TOKEN_MIGRATION_COMPLETED', '2025-05-28', 'Token限制架构迁移完成标记', 1, NOW());

-- 更新已存在的记录
UPDATE sys_params
SET param_value = '2025-05-28', update_date = NOW()
WHERE param_code = 'TOKEN_MIGRATION_COMPLETED';