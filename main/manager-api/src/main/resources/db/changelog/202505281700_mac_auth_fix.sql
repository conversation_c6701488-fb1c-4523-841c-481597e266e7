-- MAC地址认证系统修复
-- changeSet ID: 202505281700_mac_auth_fix
-- 作者: simonchen
-- 目的: 修复MAC认证系统配置，确保auto_register为false

-- ==================== 修复系统参数 ====================

-- 更新mac_auth.auto_register为false
UPDATE `sys_params` 
SET `param_value` = 'false', `update_date` = NOW()
WHERE `param_code` = 'mac_auth.auto_register';

-- 如果参数不存在则插入
INSERT IGNORE INTO `sys_params` (`param_code`, `param_value`, `value_type`, `param_type`, `remark`, `create_date`) 
VALUES ('mac_auth.auto_register', 'false', 'boolean', 1, '是否允许MAC地址自动注册', NOW());

-- ==================== 确保表结构正确 ====================

-- 检查并添加register_source字段到ai_mac_whitelist表
SET @register_source_exists = (
    SELECT COUNT(1) 
    FROM information_schema.COLUMNS 
    WHERE TABLE_SCHEMA = DATABASE() 
    AND TABLE_NAME = 'ai_mac_whitelist' 
    AND COLUMN_NAME = 'register_source'
);

SET @sql = IF(@register_source_exists = 0, 
    'ALTER TABLE ai_mac_whitelist ADD COLUMN register_source VARCHAR(20) DEFAULT "manual" COMMENT "注册来源(manual/auto)"',
    'SELECT "字段register_source已存在，跳过添加" AS message'
);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- 检查并添加register_time字段到ai_mac_whitelist表
SET @register_time_exists = (
    SELECT COUNT(1) 
    FROM information_schema.COLUMNS 
    WHERE TABLE_SCHEMA = DATABASE() 
    AND TABLE_NAME = 'ai_mac_whitelist' 
    AND COLUMN_NAME = 'register_time'
);

SET @sql = IF(@register_time_exists = 0, 
    'ALTER TABLE ai_mac_whitelist ADD COLUMN register_time DATETIME COMMENT "注册时间"',
    'SELECT "字段register_time已存在，跳过添加" AS message'
);
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- ==================== 验证配置 ====================

-- 验证MAC认证参数
SELECT 
    param_code as '参数代码',
    param_value as '参数值',
    remark as '说明'
FROM sys_params 
WHERE param_code LIKE 'mac_auth%' 
ORDER BY param_code;
