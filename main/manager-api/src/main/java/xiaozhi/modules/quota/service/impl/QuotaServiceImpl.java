package xiaozhi.modules.quota.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import xiaozhi.common.page.PageData;
import xiaozhi.common.service.impl.BaseServiceImpl;
import xiaozhi.modules.agent.dao.AgentDao;
import xiaozhi.modules.agent.entity.AgentEntity;
import xiaozhi.modules.device.dao.DeviceDao;
import xiaozhi.modules.device.entity.DeviceEntity;
import xiaozhi.modules.device.service.DeviceService;
import xiaozhi.modules.quota.dao.QuotaSettingsDao;
import xiaozhi.modules.quota.dao.QuotaUsageDao;
import xiaozhi.modules.quota.dto.QuotaSettingsDTO;
import xiaozhi.modules.quota.dto.QuotaUsageDTO;
import xiaozhi.modules.quota.dto.QuotaLimitDTO;
import xiaozhi.modules.quota.entity.QuotaSettingsEntity;
import xiaozhi.modules.quota.entity.QuotaUsageEntity;
import xiaozhi.modules.quota.service.QuotaService;
import xiaozhi.modules.sys.dao.SysParamsDao;
import xiaozhi.modules.sys.dao.SysUserDao;
import xiaozhi.modules.sys.entity.SysUserEntity;
import xiaozhi.common.redis.RedisUtils;

import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.List;

/**
 * 配额服务实现
 *
 * <AUTHOR>
 * @version 1.0, 2025/5/30
 * @since 1.0.0
 */
@Service
@AllArgsConstructor
public class QuotaServiceImpl extends BaseServiceImpl<QuotaSettingsDao, QuotaSettingsEntity> implements QuotaService {

    private final QuotaUsageDao quotaUsageDao;
    private final SysParamsDao sysParamsDao;
    private final DeviceDao deviceDao;
    private final AgentDao agentDao;
    private final SysUserDao sysUserDao;
    private final RedisUtils redisUtils;

    @Override
    public Map<String, Object> getQuotaSettings() {
        Map<String, Object> result = new HashMap<>();

        // 获取设备配额设置
        QuotaSettingsEntity deviceQuota = baseDao.getByQuotaType("device");
        if (deviceQuota != null) {
            result.put("deviceDefaultQuota", deviceQuota.getQuotaValue());
            result.put("deviceResetType", deviceQuota.getResetType());
        } else {
            result.put("deviceDefaultQuota", 10000);
            result.put("deviceResetType", "daily");
        }

        // 获取账号配额设置
        QuotaSettingsEntity accountQuota = baseDao.getByQuotaType("account");
        if (accountQuota != null) {
            result.put("accountDefaultQuota", accountQuota.getQuotaValue());
            result.put("accountResetType", accountQuota.getResetType());
        } else {
            result.put("accountDefaultQuota", 100000);
            result.put("accountResetType", "daily");
        }

        // 获取是否启用配额限制
        String enableQuotaLimit = sysParamsDao.getValueByCode("enable_quota_limit");
        result.put("enableQuotaLimit", "true".equals(enableQuotaLimit));

        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateQuotaSettings(QuotaSettingsDTO dto) {
        // 更新设备配额设置
        QuotaSettingsEntity deviceQuota = baseDao.getByQuotaType("device");
        if (deviceQuota == null) {
            deviceQuota = new QuotaSettingsEntity();
            deviceQuota.setQuotaType("device");
            deviceQuota.setDescription("设备每日配额");
            deviceQuota.setCreateDate(new Date());
        }
        deviceQuota.setQuotaValue(dto.getDeviceDefaultQuota());
        deviceQuota.setResetType(dto.getDeviceResetType());
        deviceQuota.setUpdateDate(new Date());
        if (deviceQuota.getId() == null) {
            insert(deviceQuota);
        } else {
            updateById(deviceQuota);
        }

        // 更新账号配额设置
        QuotaSettingsEntity accountQuota = baseDao.getByQuotaType("account");
        if (accountQuota == null) {
            accountQuota = new QuotaSettingsEntity();
            accountQuota.setQuotaType("account");
            accountQuota.setDescription("账号每日配额");
            accountQuota.setCreateDate(new Date());
            insert(accountQuota);
        } else {
            accountQuota.setQuotaValue(dto.getAccountDefaultQuota());
            accountQuota.setResetType(dto.getAccountResetType());
            accountQuota.setUpdateDate(new Date());
            updateById(accountQuota);
        }

        // 更新是否启用配额限制
        sysParamsDao.updateValueByCode("enable_quota_limit", dto.getEnableQuotaLimit() ? "true" : "false");
    }

    @Override
    public PageData<QuotaUsageDTO> page(Map<String, Object> params) {
        // 创建正确类型的分页对象
        long curPage = 1;
        long limit = 10;

        if (params.get("page") != null) {
            curPage = Long.parseLong((String) params.get("page"));
        }
        if (params.get("limit") != null) {
            limit = Long.parseLong((String) params.get("limit"));
        }

        Page<QuotaUsageDTO> page = new Page<>(curPage, limit);

        // 调用DAO的分页方法
        IPage<QuotaUsageDTO> resultPage = quotaUsageDao.page(page, params);

        // 填充用户名、设备别名、智能体名称等信息
        for (QuotaUsageDTO dto : resultPage.getRecords()) {
            // 填充用户名
            if (dto.getUserId() != null) {
                SysUserEntity user = sysUserDao.selectById(dto.getUserId());
                if (user != null) {
                    dto.setUsername(user.getUsername());
                }
            }

            // 填充设备别名
            if (StringUtils.isNotBlank(dto.getDeviceMac())) {
                QueryWrapper<DeviceEntity> wrapper = new QueryWrapper<>();
                wrapper.eq("mac_address", dto.getDeviceMac());
                DeviceEntity device = deviceDao.selectOne(wrapper);
                if (device != null) {
                    dto.setDeviceAlias(device.getAlias());
                }
            }

            // 填充智能体名称
            if (dto.getAgentId() != null) {
                AgentEntity agent = agentDao.selectById(dto.getAgentId());
                if (agent != null) {
                    dto.setAgentName(agent.getAgentName());
                }
            }
        }

        return new PageData<>(resultPage.getRecords(), resultPage.getTotal());
    }

    @Override
    public Map<String, Object> getUserQuotaUsage(Long userId) {
        Map<String, Object> result = new HashMap<>();

        // 获取账号配额设置
        QuotaSettingsEntity accountQuota = baseDao.getByQuotaType("account");
        int quotaLimit = accountQuota != null ? accountQuota.getQuotaValue() : 100000;

        // 获取当日使用量
        Date today = new Date();
        Integer usedQuota = quotaUsageDao.getUserDailyUsage(userId, today);
        if (usedQuota == null) {
            usedQuota = 0;
        }

        result.put("quotaLimit", quotaLimit);
        result.put("usedQuota", usedQuota);
        result.put("remainingQuota", quotaLimit > 0 ? quotaLimit - usedQuota : -1);

        return result;
    }

    @Override
    public Map<String, Object> getDeviceQuotaUsage(String macAddress) {
        Map<String, Object> result = new HashMap<>();

        // 获取设备配额设置
        QuotaSettingsEntity deviceQuota = baseDao.getByQuotaType("device");
        int quotaLimit = deviceQuota != null ? deviceQuota.getQuotaValue() : 10000;

        // 获取当日使用量
        Date today = new Date();
        Integer usedQuota = quotaUsageDao.getDeviceDailyUsage(macAddress, today);
        if (usedQuota == null) {
            usedQuota = 0;
        }

        result.put("quotaLimit", quotaLimit);
        result.put("usedQuota", usedQuota);
        result.put("remainingQuota", quotaLimit > 0 ? quotaLimit - usedQuota : -1);

        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void recordQuotaUsage(Long userId, String deviceMac, String agentId, String usageType, int usageValue) {
        QuotaUsageEntity entity = new QuotaUsageEntity();
        entity.setUserId(userId);
        entity.setDeviceMac(deviceMac);
        entity.setAgentId(agentId);
        entity.setUsageType(usageType);
        entity.setUsageValue(usageValue);
        entity.setUsageDate(new Date());
        entity.setCreateDate(new Date());

        quotaUsageDao.insert(entity);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void resetQuota(String type) {
        QueryWrapper<QuotaUsageEntity> wrapper = new QueryWrapper<>();

        if ("all".equals(type)) {
            // 重置所有配额
            quotaUsageDao.delete(wrapper);
        } else if ("device".equals(type)) {
            // 重置设备配额
            wrapper.isNotNull("device_mac");
            quotaUsageDao.delete(wrapper);
        } else if ("account".equals(type)) {
            // 重置账号配额
            wrapper.isNotNull("user_id");
            quotaUsageDao.delete(wrapper);
        }
    }

    @Override
    public Boolean isDefaultAgent(String agentId) {
        if (StringUtils.isBlank(agentId)) {
            return false;
        }
        
        AgentEntity agent = agentDao.selectById(agentId);
        if (agent == null) {
            return false;
        }
        
        // 判断是否为官方默认智能体
        // 这里假设agentCode为"DEFAULT"或"OFFICIAL"的为默认智能体
        // 根据实际业务逻辑调整判断条件
        String agentCode = agent.getAgentCode();
        return StringUtils.isNotBlank(agentCode) && 
               ("DEFAULT".equalsIgnoreCase(agentCode) || "OFFICIAL".equalsIgnoreCase(agentCode));
    }

    @Override
    public QuotaLimitDTO checkDeviceQuotaLimit(String macAddress, String agentId, int requestedQuota) {
        QuotaLimitDTO result = new QuotaLimitDTO();
        result.setMacAddress(macAddress);
        result.setAgentId(agentId);
        result.setLimitType("device");
        
        // 检查是否启用配额限制
        String enableQuotaLimit = sysParamsDao.getValueByCode("enable_quota_limit");
        if (!"true".equals(enableQuotaLimit)) {
            // 未启用配额限制，返回无限额度
            result.setDeviceDailyLimit(-1);
            result.setRemainingQuota(-1);
            result.setUsedQuota(0);
            return result;
        }
        
        // 判断是否为默认智能体
        Boolean isDefault = isDefaultAgent(agentId);
        result.setIsDefault(isDefault);
        
        // 非默认智能体不受设备配额限制
        if (!isDefault) {
            result.setDeviceDailyLimit(-1);
            result.setRemainingQuota(-1);
            result.setUsedQuota(0);
            return result;
        }
        
        // 获取设备每日限额
        Integer deviceDailyLimit = getDeviceDailyLimit();
        result.setDeviceDailyLimit(deviceDailyLimit);
        
        // 获取当日已使用额度
        Date today = new Date();
        Integer usedQuota = quotaUsageDao.getDeviceDailyUsage(macAddress, today);
        if (usedQuota == null) {
            usedQuota = 0;
        }
        result.setUsedQuota(usedQuota);
        
        // 计算剩余额度
        int remainingQuota = deviceDailyLimit > 0 ? deviceDailyLimit - usedQuota : -1;
        result.setRemainingQuota(remainingQuota);
        
        return result;
    }

    @Override
    public QuotaLimitDTO checkAccountQuotaLimit(Long userId, int requestedQuota) {
        QuotaLimitDTO result = new QuotaLimitDTO();
        result.setUserId(userId);
        result.setLimitType("account");
        
        // 检查是否启用配额限制
        String enableQuotaLimit = sysParamsDao.getValueByCode("enable_quota_limit");
        if (!"true".equals(enableQuotaLimit)) {
            // 未启用配额限制，返回无限额度
            result.setAccountDailyLimit(-1);
            result.setRemainingQuota(-1);
            result.setUsedQuota(0);
            return result;
        }
        
        // 获取账号每日限额
        Integer accountDailyLimit = getAccountDailyLimit(userId);
        result.setAccountDailyLimit(accountDailyLimit);
        
        // 获取当日已使用额度
        Date today = new Date();
        Integer usedQuota = quotaUsageDao.getUserDailyUsage(userId, today);
        if (usedQuota == null) {
            usedQuota = 0;
        }
        result.setUsedQuota(usedQuota);
        
        // 计算剩余额度
        int remainingQuota = accountDailyLimit > 0 ? accountDailyLimit - usedQuota : -1;
        result.setRemainingQuota(remainingQuota);
        
        return result;
    }

    @Override
    public Integer getAccountDailyLimit(Long userId) {
        // 获取账号配额设置
        QuotaSettingsEntity accountQuota = baseDao.getByQuotaType("account");
        int defaultLimit = accountQuota != null ? accountQuota.getQuotaValue() : 100000;
        
        // 这里可以根据用户ID查询特定用户的自定义配额
        // 如果没有特殊配置，则返回默认配额
        
        return defaultLimit;
    }

    @Override
    public Integer getDeviceDailyLimit() {
        // 获取设备配额设置
        QuotaSettingsEntity deviceQuota = baseDao.getByQuotaType("device");
        return deviceQuota != null ? deviceQuota.getQuotaValue() : 10000;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void recordTokenUsage(String deviceMac, int inputTokens, int outputTokens, String modelName) {
        // 获取设备绑定的智能体
        QueryWrapper<DeviceEntity> deviceWrapper = new QueryWrapper<>();
        deviceWrapper.eq("mac_address", deviceMac);
        DeviceEntity device = deviceDao.selectOne(deviceWrapper);
        
        if (device == null) {
            throw new RenException("设备不存在: " + deviceMac);
        }
        
        // 获取智能体信息
        AgentEntity agent = agentDao.selectById(device.getAgentId());
        if (agent == null) {
            throw new RenException("智能体不存在: " + device.getAgentId());
        }
        
        int totalTokens = inputTokens + outputTokens;
        
        // 创建Token使用记录
        QuotaUsageEntity usage = new QuotaUsageEntity();
        usage.setUserId(agent.getUserId()); // 可能为NULL(默认智能体)
        usage.setDeviceMac(deviceMac);
        usage.setAgentId(agent.getId());
        usage.setInputTokens(inputTokens);
        usage.setOutputTokens(outputTokens);
        usage.setTotalTokens(totalTokens);
        usage.setModelName(modelName);
        usage.setUsageDate(new Date());
        usage.setCreateDate(new Date());
        
        // 向后兼容：设置旧字段
        usage.setUsageValue(totalTokens);
        usage.setUsageType("token");
        
        quotaUsageDao.insert(usage);
        
        // 清理缓存
        if (agent.getUserId() != null) {
            invalidateAccountUsageCache(agent.getUserId());
        } else {
            invalidateDeviceUsageCache(deviceMac);
        }
    }

    @Override
    public QuotaLimitDTO checkTokenQuotaLimit(String deviceMac, int inputTokens, int outputTokens) {
        int totalTokens = inputTokens + outputTokens;
        
        // 获取设备绑定的智能体
        QueryWrapper<DeviceEntity> deviceWrapper = new QueryWrapper<>();
        deviceWrapper.eq("mac_address", deviceMac);
        DeviceEntity device = deviceDao.selectOne(deviceWrapper);
        
        if (device == null) {
            throw new RenException("设备不存在: " + deviceMac);
        }
        
        AgentEntity agent = agentDao.selectById(device.getAgentId());
        if (agent == null) {
            throw new RenException("智能体不存在: " + device.getAgentId());
        }
        
        if (agent.getUserId() == null) {
            // 默认智能体：检查设备级配额
            return checkDeviceQuotaLimit(deviceMac, device.getAgentId(), totalTokens);
        } else {
            // 自定义智能体：检查账号级配额
            return checkAccountQuotaLimit(agent.getUserId(), totalTokens);
        }
    }

    @Override
    public int calculateAccountTokenUsage(Long userId, Date date) {
        // 尝试从缓存获取
        String cacheKey = "token_usage:" + userId + ":" + formatDate(date);
        Integer cachedUsage = redisUtils.get(cacheKey, Integer.class);
        if (cachedUsage != null) {
            return cachedUsage;
        }
        
        // 获取账号下所有自定义智能体
        QueryWrapper<AgentEntity> agentWrapper = new QueryWrapper<>();
        agentWrapper.eq("user_id", userId);
        List<AgentEntity> customAgents = agentDao.selectList(agentWrapper);
        
        int totalTokens = 0;
        for (AgentEntity agent : customAgents) {
            Map<String, Integer> agentUsage = getAgentTokenUsage(agent.getId(), date);
            totalTokens += agentUsage.getOrDefault("totalTokens", 0);
        }
        
        // 缓存结果(TTL: 1小时)
        redisUtils.set(cacheKey, totalTokens, 3600);
        
        return totalTokens;
    }

    @Override
    public Map<String, Integer> getAgentTokenUsage(String agentId, Date date) {
        Map<String, Integer> result = new HashMap<>();
        
        // 查询智能体当日Token使用量
        QueryWrapper<QuotaUsageEntity> wrapper = new QueryWrapper<>();
        wrapper.eq("agent_id", agentId);
        wrapper.eq("usage_date", date);
        
        List<QuotaUsageEntity> usageList = quotaUsageDao.selectList(wrapper);
        
        int inputTokens = 0;
        int outputTokens = 0;
        int totalTokens = 0;
        
        for (QuotaUsageEntity usage : usageList) {
            inputTokens += usage.getInputTokens() != null ? usage.getInputTokens() : 0;
            outputTokens += usage.getOutputTokens() != null ? usage.getOutputTokens() : 0;
            totalTokens += usage.getTotalTokens() != null ? usage.getTotalTokens() : 0;
        }
        
        result.put("inputTokens", inputTokens);
        result.put("outputTokens", outputTokens);
        result.put("totalTokens", totalTokens);
        
        return result;
    }

    @Override
    public int getDeviceTokenUsage(String deviceMac, Date date) {
        // 查询设备当日Token使用量
        QueryWrapper<QuotaUsageEntity> wrapper = new QueryWrapper<>();
        wrapper.eq("device_mac", deviceMac);
        wrapper.eq("usage_date", date);
        
        List<QuotaUsageEntity> usageList = quotaUsageDao.selectList(wrapper);
        
        int totalTokens = 0;
        for (QuotaUsageEntity usage : usageList) {
            totalTokens += usage.getTotalTokens() != null ? usage.getTotalTokens() : 0;
        }
        
        return totalTokens;
    }

    /**
     * 清理账号Token使用量缓存
     */
    private void invalidateAccountUsageCache(Long userId) {
        String today = formatDate(new Date());
        String cacheKey = "token_usage:" + userId + ":" + today;
        redisUtils.delete(cacheKey);
    }

    /**
     * 清理设备Token使用量缓存
     */
    private void invalidateDeviceUsageCache(String deviceMac) {
        // 设备级缓存可以根据需要实现
        // 这里暂时不做缓存处理，因为设备使用量查询相对简单
    }

    /**
     * 格式化日期为字符串
     */
    private String formatDate(Date date) {
        LocalDate localDate = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        return localDate.toString();
    }
}
