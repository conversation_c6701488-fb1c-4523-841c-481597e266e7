"""
认证中间件：MAC地址认证 或 Token认证
- 启用MAC认证时：只使用MAC地址认证，失败时提示去官网激活
- 未启用MAC认证时：使用Token认证
- 配置manager-api后，所有MAC认证参数从后端API获取
"""

import asyncio
import re
from typing import Dict, Any
from loguru import logger

from core.auth import AuthMiddleware, AuthenticationError


class UnregisteredMacError(AuthenticationError):
    """未注册的MAC地址异常"""
    def __init__(self, mac_address):
        self.mac_address = mac_address
        super().__init__(f"未注册的MAC地址: {mac_address}，请前往官网注册您的设备")


def _is_valid_mac_format(mac_address: str) -> bool:
    """检查MAC地址格式是否有效"""
    if not mac_address:
        return False
    # 支持 XX:XX:XX:XX:XX:XX 和 XX-XX-XX-XX-XX-XX 格式
    mac_pattern = r'^([0-9A-Fa-f]{2}[:-]){5}([0-9A-Fa-f]{2})$'
    return bool(re.match(mac_pattern, mac_address))


class HybridAuthMiddleware:
    """认证中间件：MAC地址认证 或 Token认证"""

    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.mac_auth_config = config.get("mac_auth", {})

        # 初始化Token认证中间件
        self.token_auth = AuthMiddleware(config)

        # API配置
        self.api_config = config.get("manager-api", {})
        self.has_manager_api = bool(self.api_config.get("url") and self.api_config.get("secret"))

        # 配置缓存（用于动态更新）
        self._config_cache = {}
        self._config_cache_time = 0
        self._config_cache_ttl = 60  # 缓存60秒

        # 获取初始配置
        self.mac_auth_enabled, self.auto_register_enabled = self._get_mac_auth_config()

        logger.info(f"认证中间件初始化完成: MAC认证{'启用' if self.mac_auth_enabled else '禁用'}, "
                   f"自动注册{'启用' if self.auto_register_enabled else '禁用'}, "
                   f"Manager API{'已配置' if self.has_manager_api else '未配置'}")

    def _get_mac_auth_config(self) -> tuple[bool, bool]:
        """获取MAC认证配置（支持动态更新）"""
        import time

        current_time = time.time()

        # 检查缓存是否有效
        if (current_time - self._config_cache_time) < self._config_cache_ttl and self._config_cache:
            return self._config_cache.get("mac_auth_enabled", False), self._config_cache.get("auto_register_enabled", False)

        # 如果有Manager API，从API获取配置
        if self.has_manager_api:
            try:
                import aiohttp
                import asyncio

                async def fetch_config():
                    url = f"{self.api_config['url']}/config/mac-auth"
                    headers = {
                        "Authorization": f"Bearer {self.api_config['secret']}",
                        "Content-Type": "application/json"
                    }

                    async with aiohttp.ClientSession() as session:
                        async with session.get(url, headers=headers, timeout=aiohttp.ClientTimeout(total=5)) as response:
                            if response.status == 200:
                                data = await response.json()
                                if data.get("code") == 0:
                                    config_data = data.get("data", {})
                                    return config_data.get("enabled", False), config_data.get("auto_register", False)
                    return None, None

                # 尝试获取配置
                try:
                    loop = asyncio.get_event_loop()
                    mac_enabled, auto_register = loop.run_until_complete(fetch_config())

                    if mac_enabled is not None:
                        # 更新缓存
                        self._config_cache = {
                            "mac_auth_enabled": mac_enabled,
                            "auto_register_enabled": auto_register
                        }
                        self._config_cache_time = current_time
                        logger.info(f"从API获取MAC认证配置: enabled={mac_enabled}, auto_register={auto_register}")
                        return mac_enabled, auto_register
                except Exception as e:
                    logger.warning(f"从API获取MAC认证配置失败: {e}")
            except Exception as e:
                logger.warning(f"MAC认证配置获取异常: {e}")

        # 回退到本地配置
        mac_enabled = self.mac_auth_config.get("enabled", False)
        auto_register = self.mac_auth_config.get("auto_register", False)

        # 更新缓存
        self._config_cache = {
            "mac_auth_enabled": mac_enabled,
            "auto_register_enabled": auto_register
        }
        self._config_cache_time = current_time

        logger.info(f"使用本地MAC认证配置: enabled={mac_enabled}, auto_register={auto_register}")
        return mac_enabled, auto_register

    async def authenticate(self, headers: Dict[str, str]) -> bool:
        """
        认证流程：
        1. 如果启用MAC认证，优先使用MAC认证
        2. MAC认证失败时，抛出UnregisteredMacError异常
        3. 如果未启用MAC认证，直接使用Token认证
        """
        device_mac = headers.get("device-id", "")

        # 动态获取最新的MAC认证配置
        self.mac_auth_enabled, self.auto_register_enabled = self._get_mac_auth_config()

        # 1. 尝试MAC地址预认证
        if self.mac_auth_enabled and device_mac:
            try:
                # 检查MAC地址格式
                if not _is_valid_mac_format(device_mac):
                    logger.warning(f"MAC地址格式无效: {device_mac}，抛出UnregisteredMacError")
                    raise UnregisteredMacError(device_mac)
                else:
                    # 检查白名单
                    if await self._check_mac_whitelist(device_mac):
                        logger.info(f"MAC地址预认证通过: {device_mac}")
                        headers["auth_success"] = "true"
                        headers["auth_method"] = "mac_whitelist"
                        headers["device_bound"] = "true"
                        return True
                    else:
                        logger.info(f"MAC地址不在白名单中: {device_mac}")

                        # 2. 尝试自动注册
                        if self.auto_register_enabled:
                            logger.info(f"开始尝试自动注册MAC地址: {device_mac}")
                            if await self._auto_register_mac_address(device_mac):
                                logger.info(f"MAC地址自动注册成功: {device_mac}")
                                headers["auth_success"] = "true"
                                headers["auth_method"] = "auto_register"
                                headers["device_bound"] = "true"
                                return True
                            else:
                                logger.warning(f"MAC地址自动注册失败: {device_mac}，抛出UnregisteredMacError")
                                raise UnregisteredMacError(device_mac)
                        else:
                            logger.info(f"自动注册未启用，抛出UnregisteredMacError")
                            raise UnregisteredMacError(device_mac)
            except UnregisteredMacError:
                # 重新抛出UnregisteredMacError异常
                raise
            except Exception as e:
                logger.warning(f"MAC地址认证失败: {str(e)}，抛出UnregisteredMacError")
                raise UnregisteredMacError(device_mac)

        # 3. 回退到Token认证（仅当未启用MAC认证时）
        logger.info("使用Token认证")
        return await self.token_auth.authenticate(headers)

    async def _check_mac_whitelist(self, mac_address: str) -> bool:
        """实时检查MAC地址是否在白名单中"""
        if not self.api_config.get("url") or not self.api_config.get("secret"):
            logger.warning("API配置不完整，无法检查MAC地址白名单")
            return False

        import aiohttp

        url = f"{self.api_config['url']}/device/auth/check/{mac_address}"
        headers = {
            "Authorization": f"Bearer {self.api_config['secret']}",
            "Content-Type": "application/json"
        }

        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(url, headers=headers, timeout=aiohttp.ClientTimeout(total=5)) as response:
                    if response.status == 200:
                        data = await response.json()
                        if data.get("code") == 0:
                            is_whitelisted = data.get("data", False)
                            logger.info(f"MAC地址 {mac_address} 白名单检查结果: {is_whitelisted}")
                            return is_whitelisted
                        else:
                            logger.warning(f"MAC地址检查API返回错误: {data}")
                            return False
                    else:
                        logger.error(f"MAC地址检查API请求失败: HTTP {response.status}")
                        return False
        except asyncio.TimeoutError:
            logger.error(f"MAC地址检查超时: {mac_address}")
            return False
        except Exception as e:
            logger.error(f"MAC地址检查失败: {str(e)}")
            return False

    async def _auto_register_mac_address(self, mac_address: str) -> bool:
        """自动注册MAC地址到白名单"""
        if not self.api_config.get("url") or not self.api_config.get("secret"):
            logger.warning("API配置不完整，无法进行MAC地址自动注册")
            return False

        import aiohttp

        url = f"{self.api_config['url']}/device/mac/auto-register"
        headers = {
            "Authorization": f"Bearer {self.api_config['secret']}",
            "Content-Type": "application/json"
        }

        register_data = {
            "macAddress": mac_address,
            "remark": "自动注册设备"
        }

        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(url, headers=headers, json=register_data, timeout=aiohttp.ClientTimeout(total=10)) as response:
                    if response.status == 200:
                        data = await response.json()
                        if data.get("code") == 0:
                            logger.info(f"MAC地址 {mac_address} 自动注册成功")
                            return True
                        else:
                            logger.error(f"MAC地址自动注册API返回错误: {data.get('msg', '未知错误')}")
                            return False
                    else:
                        logger.error(f"MAC地址自动注册API请求失败: HTTP {response.status}")
                        return False
        except asyncio.TimeoutError:
            logger.error(f"MAC地址自动注册超时: {mac_address}")
            return False
        except Exception as e:
            logger.error(f"MAC地址自动注册失败: {str(e)}")
            return False
