from config.logger import setup_logging
import json
from plugins_func.register import (
    FunctionRegistry,
    ActionResponse,
    Action,
    ToolType,
    DeviceTypeRegistry,
)
from plugins_func.functions.hass_init import append_devices_to_prompt

TAG = __name__


class FunctionHandler:
    def __init__(self, conn):
        self.conn = conn
        self.config = conn.config
        self.device_type_registry = DeviceTypeRegistry()
        self.function_registry = FunctionRegistry()
        self.register_nessary_functions()
        self.register_config_functions()
        self.functions_desc = self.function_registry.get_all_function_desc()
        func_names = self.current_support_functions()
        self.modify_plugin_loader_des(func_names)
        self.finish_init = True

    def modify_plugin_loader_des(self, func_names):
        if "plugin_loader" not in func_names:
            return
        # 可编辑的列表中去掉plugin_loader
        surport_plugins = [func for func in func_names if func != "plugin_loader"]
        func_names = ",".join(surport_plugins)
        for function_desc in self.functions_desc:
            if function_desc["function"]["name"] == "plugin_loader":
                function_desc["function"]["description"] = function_desc["function"][
                    "description"
                ].replace("[plugins]", func_names)
                break

    def upload_functions_desc(self):
        self.functions_desc = self.function_registry.get_all_function_desc()

    def current_support_functions(self):
        func_names = []
        for func in self.functions_desc:
            func_names.append(func["function"]["name"])
        # 打印当前支持的函数列表
        self.conn.logger.bind(tag=TAG, session_id=self.conn.session_id).info(
            f"当前支持的函数列表: {func_names}"
        )
        return func_names

    def get_functions(self):
        """获取功能调用配置"""
        return self.functions_desc

    def register_nessary_functions(self):
        """注册必要的函数"""
        self.function_registry.register_function("handle_exit_intent")
        self.function_registry.register_function("plugin_loader")
        self.function_registry.register_function("get_time")
        self.function_registry.register_function("get_lunar")
        self.function_registry.register_function("handle_speaker_volume_or_screen_brightness")
        self.function_registry.register_function("handle_movement")

    def register_config_functions(self):
        """注册配置中的函数,可以不同客户端使用不同的配置"""
        for func in self.config["Intent"][self.config["selected_module"]["Intent"]].get(
            "functions", []
        ):
            self.function_registry.register_function(func)

        """home assistant需要初始化提示词"""
        append_devices_to_prompt(self.conn)

    def get_function(self, name):
        return self.function_registry.get_function(name)

    def handle_llm_function_call(self, conn, function_call_data):
        try:
            function_name = function_call_data["name"]
            self.conn.logger.bind(tag=TAG).info(f"🚀 [FUNCTION_HANDLER] 开始处理函数调用")
            self.conn.logger.bind(tag=TAG).info(f"📋 [FUNCTION_CALL] 函数名: {function_name}")
            self.conn.logger.bind(tag=TAG).info(f"📋 [FUNCTION_DATA] 完整调用数据: {function_call_data}")

            funcItem = self.get_function(function_name)
            if not funcItem:
                self.conn.logger.bind(tag=TAG).error(f"❌ [FUNCTION_ERROR] 未找到函数: {function_name}")
                available_functions = [desc["function"]["name"] for desc in self.functions_desc]
                self.conn.logger.bind(tag=TAG).info(f"📋 [AVAILABLE_FUNCTIONS] 可用函数: {available_functions}")
                return ActionResponse(
                    action=Action.NOTFOUND, result="没有找到对应的函数", response=""
                )

            self.conn.logger.bind(tag=TAG).info(f"✅ [FUNCTION_FOUND] 找到函数: {function_name}, 类型: {funcItem.type}")

            func = funcItem.func
            arguments = function_call_data["arguments"]
            arguments = json.loads(arguments) if arguments else {}
            self.conn.logger.bind(tag=TAG).info(f"📋 [FUNCTION_ARGS] 解析参数: {arguments}")

            self.conn.logger.bind(tag=TAG).info(f"🎯 [FUNCTION_EXEC] 开始执行函数: {function_name}")

            if (
                funcItem.type == ToolType.SYSTEM_CTL
                or funcItem.type == ToolType.IOT_CTL
            ):
                self.conn.logger.bind(tag=TAG).debug(f"🔧 [FUNCTION_TYPE] 执行系统/IoT控制函数")
                result = func(conn, **arguments)
            elif funcItem.type == ToolType.WAIT:
                self.conn.logger.bind(tag=TAG).debug(f"⏰ [FUNCTION_TYPE] 执行等待类型函数")
                result = func(**arguments)
            elif funcItem.type == ToolType.CHANGE_SYS_PROMPT:
                self.conn.logger.bind(tag=TAG).debug(f"💬 [FUNCTION_TYPE] 执行系统提示词变更函数")
                result = func(conn, **arguments)
            else:
                self.conn.logger.bind(tag=TAG).error(f"❌ [FUNCTION_TYPE_ERROR] 未知函数类型: {funcItem.type}")
                return ActionResponse(
                    action=Action.NOTFOUND, result="没有找到对应的函数", response=""
                )

            self.conn.logger.bind(tag=TAG).info(f"✅ [FUNCTION_RESULT] 函数执行完成: {function_name}")
            self.conn.logger.bind(tag=TAG).info(f"📋 [FUNCTION_RESULT] 执行结果: action={result.action}, result={result.result}")
            return result

        except Exception as e:
            self.conn.logger.bind(tag=TAG).error(f"❌ [FUNCTION_ERROR] 处理function call错误: {e}")
            self.conn.logger.bind(tag=TAG).error(f"🔍 [ERROR_DETAIL] 异常详情: {type(e).__name__}: {e}")
            import traceback
            self.conn.logger.bind(tag=TAG).error(f"📋 [ERROR_TRACEBACK] 错误堆栈: {traceback.format_exc()}")

        return None
