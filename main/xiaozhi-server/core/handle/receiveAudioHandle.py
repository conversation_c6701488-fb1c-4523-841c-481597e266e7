import time
import copy
from core.utils.util import remove_punctuation_and_length
from core.handle.sendAudioHandle import send_stt_message
from core.handle.intentHandler import handle_user_intent
from core.utils.output_counter import check_device_output_limit
from core.handle.reportHandle import enqueue_asr_report
from core.handle.sendAudioHandle import SentenceType
from core.utils.util import audio_to_data
from core.providers.tts.dto.dto import ContentType

TAG = __name__


def detect_language_from_text(text):
    """从文本中检测语言"""
    if not text:
        return "zh-CN"

    # 检测中文字符
    chinese_chars = len([c for c in text if '\u4e00' <= c <= '\u9fff'])
    # 检测日文字符（平假名、片假名、汉字在日文中的使用）
    japanese_chars = len([c for c in text if '\u3040' <= c <= '\u309f' or '\u30a0' <= c <= '\u30ff'])
    # 检测英文单词
    english_words = len([w for w in text.split() if w.isascii() and w.isalpha() and len(w) > 1])

    total_chars = len(text.replace(" ", ""))
    if total_chars == 0:
        return "zh-CN"

    # 常见的语言特征词汇检测
    text_lower = text.lower()

    # 英文特征词
    english_indicators = ["hello", "hi", "thank", "please", "device", "activate", "website", "control", "panel"]
    english_score = sum(1 for word in english_indicators if word in text_lower)

    # 日文特征词（平假名/片假名常见词）
    japanese_indicators = ["こんにちは", "ありがとう", "デバイス", "アクティベート", "サイト"]
    japanese_score = sum(1 for word in japanese_indicators if word in text)

    # 中文特征词
    chinese_indicators = ["你好", "谢谢", "设备", "激活", "网站", "控制", "面板", "绑定"]
    chinese_score = sum(1 for word in chinese_indicators if word in text)

    # 综合判断
    if english_score > 0 or (english_words > 2 and chinese_chars / total_chars < 0.1):
        return "en-US"
    elif japanese_score > 0 or (japanese_chars / total_chars > 0.1):
        return "ja-JP"
    elif chinese_score > 0 or chinese_chars / total_chars > 0.2:
        return "zh-CN"
    elif english_words > 1:
        return "en-US"
    else:
        return "zh-CN"  # 默认中文


def detect_language_from_history(conn):
    """从对话历史和最近输入中检测用户使用的语言"""
    # 优先使用最近的用户输入（如果有的话）
    if hasattr(conn, 'last_user_input') and conn.last_user_input:
        return detect_language_from_text(conn.last_user_input)

    # 如果没有最近输入，从对话历史中检测
    if not hasattr(conn, 'dialogue') or not conn.dialogue:
        return "zh-CN"  # 默认中文

    # 获取最近的用户消息
    recent_messages = []
    try:
        dialogue_history = conn.dialogue.get_llm_dialogue()
        for msg in dialogue_history[-5:]:  # 检查最近5条消息
            if msg.get("role") == "user":
                recent_messages.append(msg.get("content", ""))
    except:
        return "zh-CN"

    if not recent_messages:
        return "zh-CN"

    # 使用文本检测函数
    text = " ".join(recent_messages)
    return detect_language_from_text(text)


def get_device_activation_message(conn):
    """获取设备激活提示消息，自动检测语言"""
    language = detect_language_from_history(conn)

    if language == "en-US":
        return "Device not activated. Please visit our website to activate your device."
    elif language == "ja-JP":
        return "デバイスがアクティベートされていません。公式サイトでデバイスをアクティベートしてください。"
    else:  # 默认中文
        return "设备未激活，请前往官网激活您的设备。"


def get_bind_device_message(conn, bind_code):
    """获取设备绑定提示消息，自动检测语言"""
    language = detect_language_from_history(conn)

    if language == "en-US":
        return f"Please log in to the control panel and enter {bind_code} to bind the device."
    elif language == "ja-JP":
        return f"コントロールパネルにログインして、{bind_code}を入力してデバイスをバインドしてください。"
    else:  # 默认中文
        return f"请登录控制面板，输入{bind_code}，绑定设备。"


def get_device_not_found_message(conn):
    """获取设备未找到提示消息，自动检测语言"""
    language = detect_language_from_history(conn)

    if language == "en-US":
        return "Device version information not found. Please configure the OTA address correctly and recompile the firmware."
    elif language == "ja-JP":
        return "デバイスのバージョン情報が見つかりません。OTAアドレスを正しく設定してファームウェアを再コンパイルしてください。"
    else:  # 默认中文
        return "没有找到该设备的版本信息，请正确配置 OTA地址，然后重新编译固件。"


async def handleAudioMessage(conn, audio):
    if conn.vad is None:
        return
    if not conn.asr_server_receive:
        conn.logger.bind(tag=TAG).debug(f"前期数据处理中，暂停接收")
        return
    if conn.client_listen_mode == "auto" or conn.client_listen_mode == "realtime":
        have_voice = conn.vad.is_vad(conn, audio)
    else:
        have_voice = conn.client_have_voice

    # 如果本次没有声音，本段也没声音，就把声音丢弃了
    if have_voice == False and conn.client_have_voice == False:
        await no_voice_close_connect(conn)
        conn.asr_audio.append(audio)
        conn.asr_audio = conn.asr_audio[
            -10:
        ]  # 保留最新的10帧音频内容，解决ASR句首丢字问题
        return
    conn.client_no_voice_last_time = 0.0
    conn.asr_audio.append(audio)
    # 如果本段有声音，且已经停止了
    if conn.client_voice_stop:
        conn.client_abort = False
        conn.asr_server_receive = False
        # 音频太短了，无法识别
        if len(conn.asr_audio) < 15:
            conn.asr_server_receive = True
        else:
            raw_text, _ = await conn.asr.speech_to_text(
                conn.asr_audio, conn.session_id
            )  # 确保ASR模块返回原始文本
            conn.logger.bind(tag=TAG).info(f"识别文本: {raw_text}")
            text_len, _ = remove_punctuation_and_length(raw_text)
            if text_len > 0:
                # 使用自定义模块进行上报
                await startToChat(conn, raw_text)
                enqueue_asr_report(conn, raw_text, copy.deepcopy(conn.asr_audio))
            else:
                conn.asr_server_receive = True
        conn.asr_audio.clear()
        conn.reset_vad_states()


async def startToChat(conn, text):
    # 保存用户最近的输入，用于语言检测
    conn.last_user_input = text

    if conn.need_bind:
        await check_bind_device(conn)
        return

    # 如果当日的输出字数大于限定的字数
    if conn.max_output_size > 0:
        if check_device_output_limit(
            conn.headers.get("device-id"), conn.max_output_size
        ):
            await max_out_size(conn)
            return

    # 首先进行意图分析
    intent_handled = await handle_user_intent(conn, text)

    if intent_handled:
        # 如果意图已被处理，不再进行聊天
        conn.asr_server_receive = True
        return

    # 意图未被处理，继续常规聊天流程
    await send_stt_message(conn, text)
    conn.executor.submit(conn.chat, text)


async def no_voice_close_connect(conn):
    if conn.client_no_voice_last_time == 0.0:
        conn.client_no_voice_last_time = time.time() * 1000
    else:
        no_voice_time = time.time() * 1000 - conn.client_no_voice_last_time
        close_connection_no_voice_time = int(
            conn.config.get("close_connection_no_voice_time", 120)
        )
        if (
            not conn.close_after_chat
            and no_voice_time > 1000 * close_connection_no_voice_time
        ):
            conn.close_after_chat = True
            conn.client_abort = False
            conn.asr_server_receive = False
            end_prompt = conn.config.get("end_prompt", {})
            if end_prompt and end_prompt.get("enable", True) is False:
                conn.logger.bind(tag=TAG).info("结束对话，无需发送结束提示语")
                await conn.close()
                return
            prompt = end_prompt.get("prompt")
            if not prompt:
                prompt = "请你以“时间过得真快”未来头，用富有感情、依依不舍的话来结束这场对话吧。！"
            await startToChat(conn, prompt)


async def max_out_size(conn):
    text = "不好意思，我现在有点事情要忙，明天这个时候我们再聊，约好了哦！明天不见不散，拜拜！"
    await send_stt_message(conn, text)
    file_path = "config/assets/max_output_size.wav"
    opus_packets, _ = audio_to_data(file_path)
    conn.tts.tts_audio_queue.put((SentenceType.LAST, opus_packets, text))
    conn.close_after_chat = True


async def handle_unregistered_mac(conn, mac_address):
    """处理未注册的MAC地址 - 使用TTS实时生成多语言语音"""
    # 构造提示文本 - 支持多语言
    text = get_device_activation_message(conn)

    # 发送STT消息（就像用户说了话一样）
    await send_stt_message(conn, f"设备MAC地址: {mac_address}")

    # 设置TTS参数 - 与正常对话完全一致
    conn.tts_first_text_index = 0
    conn.tts_last_text_index = 0
    conn.llm_finish_task = True

    # 使用TTS实时生成语音，支持多语言
    # 检查TTS是否可用，如果不可用则使用预录制音频作为备选
    if conn.tts is not None:
        # 检查是否有speak_and_play方法和tts_queue属性
        if hasattr(conn, 'speak_and_play') and hasattr(conn, 'tts_queue'):
            future = conn.executor.submit(conn.speak_and_play, None, text, 0)
            conn.tts_queue.put((future, 0))
        else:
            # 使用TTS的标准接口
            conn.tts.tts_one_sentence(conn, ContentType.TEXT, content_detail=text)
    else:
        # TTS不可用，使用专门的MAC认证音频文件作为备选
        from core.utils.util import audio_to_data
        music_path = "config/assets/mac_device_not_activated.wav"
        opus_packets, _ = audio_to_data(music_path)
        # 确保有tts对象和tts_audio_queue
        if hasattr(conn, 'tts') and conn.tts and hasattr(conn.tts, 'tts_audio_queue'):
            conn.tts.tts_audio_queue.put((SentenceType.LAST, opus_packets, text))

    # 标记连接在语音播放完成后关闭
    conn.close_after_chat = True


async def check_bind_device(conn):
    # 检查实际的认证方式：如果有bind_code说明走的是Token认证，否则检查是否为MAC认证
    # 优先根据实际认证结果判断，而不是配置文件
    is_mac_authenticated = (
        conn.headers.get("auth_method") in ["mac_whitelist", "auto_register"] and
        conn.headers.get("auth_success") == "true"
    )

    # 如果有bind_code，说明走的是Token认证流程，不是MAC认证
    mac_auth_enabled = is_mac_authenticated and not conn.bind_code

    # 添加调试日志
    conn.logger.bind(tag=TAG).info(f"认证方式判断 - is_mac_authenticated: {is_mac_authenticated}, "
                                   f"bind_code: {conn.bind_code}, mac_auth_enabled: {mac_auth_enabled}, "
                                   f"auth_method: {conn.headers.get('auth_method')}, "
                                   f"auth_success: {conn.headers.get('auth_success')}")

    if conn.bind_code:
        # 确保bind_code是6位数字
        if len(conn.bind_code) != 6:
            conn.logger.bind(tag=TAG).error(f"无效的绑定码格式: {conn.bind_code}")
            text = "绑定码格式错误，请检查配置。"
            await send_stt_message(conn, text)
            return

        # 根据认证方式显示不同的提示信息
        if mac_auth_enabled:
            text = get_device_activation_message(conn)
        else:
            text = get_bind_device_message(conn, conn.bind_code)

        await send_stt_message(conn, text)
        conn.tts_first_text_index = 0
        conn.llm_finish_task = True

        if mac_auth_enabled:
            # MAC认证模式：只播放激活提示，不播放绑定码
            conn.tts_last_text_index = 0
            # 检查TTS是否可用，如果不可用则使用预录制音频
            if conn.tts is not None:
                # 检查是否有speak_and_play方法和tts_queue属性
                if hasattr(conn, 'speak_and_play') and hasattr(conn, 'tts_queue'):
                    future = conn.executor.submit(conn.speak_and_play, None, text, 0)
                    conn.tts_queue.put((future, 0))
                else:
                    # 使用TTS的标准接口
                    conn.tts.tts_one_sentence(conn, ContentType.TEXT, content_detail=text)
            else:
                # TTS不可用，使用专门的MAC认证音频文件
                music_path = "config/assets/mac_device_not_activated.wav"
                opus_packets, _ = audio_to_data(music_path)
                # 确保有tts对象和tts_audio_queue
                if hasattr(conn, 'tts') and conn.tts and hasattr(conn.tts, 'tts_audio_queue'):
                    conn.tts.tts_audio_queue.put((SentenceType.LAST, opus_packets, text))
        else:
            # Token认证模式：播放绑定码
            conn.tts_last_text_index = 6

            # 播放提示音
        # 播放提示音
        music_path = "config/assets/bind_code.wav"
        opus_packets, _ = audio_to_data(music_path)
        conn.tts.tts_audio_queue.put((SentenceType.FIRST, opus_packets, text))

        # 逐个播放数字
        for i in range(6):  # 确保只播放6位数字
            try:
                digit = conn.bind_code[i]
                num_path = f"config/assets/bind_code/{digit}.wav"
                num_packets, _ = audio_to_data(num_path)
                conn.tts.tts_audio_queue.put((SentenceType.MIDDLE, num_packets, None))
            except Exception as e:
                conn.logger.bind(tag=TAG).error(f"播放数字音频失败: {e}")
                continue
        conn.tts.tts_audio_queue.put((SentenceType.LAST, [], None))
    else:
        # 根据认证方式显示不同的提示信息
        if mac_auth_enabled:
            text = get_device_activation_message(conn)
        else:
            text = get_device_not_found_message(conn)

        await send_stt_message(conn, text)
        conn.tts_first_text_index = 0
        conn.tts_last_text_index = 0
        conn.llm_finish_task = True

        if mac_auth_enabled:
            # MAC认证模式：使用TTS实时生成语音，确保文本和语音一致
            # 检查TTS是否可用，如果不可用则使用预录制音频
            if conn.tts is not None:
                # 检查是否有speak_and_play方法和tts_queue属性
                if hasattr(conn, 'speak_and_play') and hasattr(conn, 'tts_queue'):
                    future = conn.executor.submit(conn.speak_and_play, None, text, 0)
                    conn.tts_queue.put((future, 0))
                else:
                    # 使用TTS的标准接口
                    conn.tts.tts_one_sentence(conn, ContentType.TEXT, content_detail=text)
            else:
                # TTS不可用，使用专门的MAC认证音频文件
                music_path = "config/assets/mac_device_not_activated.wav"
                opus_packets, _ = audio_to_data(music_path)
                # 确保有tts对象和tts_audio_queue
                if hasattr(conn, 'tts') and conn.tts and hasattr(conn.tts, 'tts_audio_queue'):
                    conn.tts.tts_audio_queue.put((SentenceType.LAST, opus_packets, text))
        else:
            # Token认证模式：使用预录制的音频文件
            music_path = "config/assets/bind_not_found.wav"
            opus_packets, _ = audio_to_data(music_path)
            conn.tts.tts_audio_queue.put((SentenceType.LAST, opus_packets, text))

